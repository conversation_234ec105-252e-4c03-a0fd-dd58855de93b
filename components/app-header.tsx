"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { Sofa } from "lucide-react"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { RefreshButton } from "@/components/ui/refresh-button"
import { NotificationIcon } from "@/components/notification-icon"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"

interface AppHeaderProps {
  title?: string
  onRefresh?: () => void | Promise<void>
  isRefreshing?: boolean
  showRefresh?: boolean
  children?: React.ReactNode
}

export function AppHeader({
  title,
  onRefresh,
  isRefreshing = false,
  showRefresh = false,
  children,
}: AppHeaderProps) {
  const [user, setUser] = useState<any>(null)
  const isMobile = useIsMobile()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser)
    })
    return () => unsubscribe()
  }, [])

  return (
    <header className="sticky top-0 py-5 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4">
        {/* Left side - Logo and Title */}
        <div className="flex items-center gap-4">
          {!isMobile && (
            <Link href="/dashboard" className="flex items-center gap-2">
              {/* <Sofa className="h-6 w-6 text-primary" /> */}
              {/* <span className="text-xl font-bold">L2L</span> */}
            </Link>
          )}
          {title && (
            <div className="hidden sm:block">
              <h1 className="text-lg font-semibold">{title}</h1>
            </div>
          )}
        </div>

        {/* Center - Custom content */}
        {children && (
          <div className="flex-1 flex justify-center px-4">
            {children}
          </div>
        )}

        {/* Right side - Actions */}
        <div className="flex items-center gap-2">
          {showRefresh && onRefresh && (
            <RefreshButton
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              size={isMobile ? "sm" : "default"}
            />
          )}
          
          {user && (
            <NotificationIcon token={user.accessToken || ""} />
          )}
          
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}