"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Package, Plus } from "lucide-react"

interface EmptyInventoryProps {
  onAddItem: () => void
}

export function EmptyInventory({ onAddItem }: EmptyInventoryProps) {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="w-48 h-48 relative mb-6">
        <Image
          src="/empty-inventory.svg"
          alt="Empty inventory"
          fill
          className="object-contain"
          // Fallback if image doesn't exist
          onError={(e) => {
            e.currentTarget.style.display = "none"
            document.getElementById("fallback-icon")!.style.display = "flex"
          }}
        />
        <div id="fallback-icon" className="hidden w-full h-full bg-purple-50 rounded-full items-center justify-center">
          <Package className="h-24 w-24 text-purple-300" />
        </div>
      </div>

      <h2 className="text-2xl font-bold text-foreground mb-3">Your inventory is empty</h2>
      <p className="text-gray-500 text-center max-w-md mb-8">
        You haven't added any products to your inventory yet. Start by adding your first product or import your existing
        inventory.
      </p>

      <div className="flex flex-col sm:flex-row gap-4">
        <Button onClick={onAddItem} className="bg-purple-600 hover:bg-purple-700 gap-2" size="lg">
          <Plus className="h-5 w-5" />
          Add Your First Product
        </Button>

        {/* <Button variant="outline" size="lg" className="gap-2">
          <FileUp className="h-5 w-5" />
          Import Inventory
        </Button> */}
      </div>

      <div className="mt-12 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg max-w-lg">
        <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Pro Tip</h3>
        <p className="text-blue-600 dark:text-blue-400 text-sm">
          Adding high-quality images and detailed descriptions can increase customer interest by up to 83%. Make sure to
          include all product dimensions and materials.
        </p>
      </div>
    </div>
  )
}
