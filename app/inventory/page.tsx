"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Plus, Filter } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { AddProductDialog } from "@/app/inventory/add-product-dialog"
import { StatsCards } from "@/app/inventory/stats-card"
import { InventoryTable } from "@/app/inventory/table"
import { EmptyInventory } from "./empty-inventory"
import { InventorySkeleton } from "./loading-skeleton"
import type { ProductType } from "@/types/product"
import { AppHeader } from "@/components/app-header"

export default function Inventory() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [inventoryData, setInventoryData] = useState<any>(null)
  const [isAddProductOpen, setIsAddProductOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [filteredItems, setFilteredItems] = useState<ProductType[]>([])

  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push("/login")
      } else {
        setUser(user)
        const token = await user.getIdToken()
        fetchInventoryData(token)
      }
    })
    return () => unsubscribe()
  }, [router])

  const fetchInventoryData = async (token: string, showRefreshToast = false) => {
    try {
      if (showRefreshToast) {
        setIsRefreshing(true)
      }
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/products/get`, {
        headers: { Authorization: `Bearer ${token}` },
      })

      if (!response.ok) throw new Error("Failed to fetch inventory")

      const products = await response.json()
      console.log("Fetched products:", products)

      processInventoryData(products)
      
      if (showRefreshToast) {
        toast({ 
          title: "Inventory refreshed", 
          description: "Your inventory data has been updated." 
        })
      }
    } catch (error) {
      console.error("Error fetching inventory data:", error)
      toast({ title: "Failed to fetch inventory data", variant: "destructive" })
      setIsLoading(false)
      // Set empty inventory data
      setInventoryData({
        totalItems: { count: 0, change: 0 },
        availableItems: { count: 0, change: 0 },
        rentedItems: { count: 0, change: 0 },
        maintenanceItems: { count: 0, change: 0 },
        items: [],
      })
      setFilteredItems([])
    } finally {
      if (showRefreshToast) {
        setIsRefreshing(false)
      }
    }
  }

  const processInventoryData = (products: any[]) => {
    // Calculate available items (quantity > 0)
    const availableCount = products.filter((p) => p.quantity > 0).length

    // Calculate rented items
    const rentedCount = products.reduce((acc, p) => acc + (p.currentlyRented || 0), 0)

    // Calculate maintenance items (those marked as in maintenance or with quantity 0)
    const maintenanceCount = products.filter((p) => p.quantity === 0).length

    setInventoryData({
      totalItems: { count: products.length, change: 5 },
      availableItems: { count: availableCount, change: 5 },
      rentedItems: { count: rentedCount, change: 5 },
      maintenanceItems: { count: maintenanceCount, change: -10 },
      items: products,
    })

    setFilteredItems(products)
    setIsLoading(false)
  }

  // Apply filters whenever search term or filters change
  useEffect(() => {
    if (!inventoryData?.items) return

    let filtered = [...inventoryData.items]

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(term) ||
          item.id.toLowerCase().includes(term) ||
          item.category.toLowerCase().includes(term),
      )
    }

    // Apply category filter
    if (categoryFilter !== "all") {
      filtered = filtered.filter((item) => item.category === categoryFilter)
    }

    // Apply status filter
    if (statusFilter !== "all") {
      switch (statusFilter) {
        case "Available":
          filtered = filtered.filter((item) => item.quantity > 10)
          break
        case "Low_stock":
          filtered = filtered.filter((item) => item.quantity > 0 && item.quantity <= 10)
          break
        case "Out_of_stock":
          filtered = filtered.filter((item) => item.quantity === 0)
          break
      }
    }

    setFilteredItems(filtered)
  }, [searchTerm, categoryFilter, statusFilter, inventoryData])

  const handleRefresh = async () => {
    if (user) {
      const token = await user.getIdToken()
      await fetchInventoryData(token, true)
    }
  }

  const handleProductAdded = () => {
    handleRefresh()
  }

  if (isLoading) return <InventorySkeleton />

  const hasInventory = inventoryData.items.length > 0

  return (
    <>
      <AppHeader
        title="Inventory"
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
        showRefresh={true}
      >
        <div className="flex items-center gap-2">
          <Button className="gap-2" onClick={() => setIsAddProductOpen(true)}>
            <Plus className="h-4 w-4" /> Add Item
          </Button>
        </div>
      </AppHeader>

      <div className="p-6 space-y-6">

      <StatsCards inventoryData={inventoryData} />

      {hasInventory ? (
        <>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search inventory..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap gap-2 items-center w-full md:w-auto">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="Living Room">Living Room</SelectItem>
                  <SelectItem value="Dining Room">Dining Room</SelectItem>
                  <SelectItem value="Bedroom">Bedroom</SelectItem>
                  <SelectItem value="Office">Office</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="Available">Available</SelectItem>
                  <SelectItem value="Low_stock">Low Stock</SelectItem>
                  <SelectItem value="Out_of_stock">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <InventoryTable items={filteredItems} />
        </>
      ) : (
        <EmptyInventory onAddItem={() => setIsAddProductOpen(true)} />
      )}

        <AddProductDialog
          open={isAddProductOpen}
          onOpenChange={setIsAddProductOpen}
          onProductAdded={handleProductAdded}
        />
      </div>
    </>
  )
}
