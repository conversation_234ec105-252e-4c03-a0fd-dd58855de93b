"use client"
import { useState, useEffect, Suspense } from "react"
import type React from "react"

import { use<PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON> } from "next/navigation"
import { toast } from "react-hot-toast"
import { FcGoogle } from "react-icons/fc"
import {
  signInWithPopup,
  GoogleAuthProvider,
  sendSignInLinkToEmail,
  isSignInWithEmailLink,
  signInWithEmailLink,
  onAuthStateChanged,
} from "firebase/auth"
import { auth } from "@/lib/firebase"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, Mail, Lock, CheckCircle, AlertCircle, Eye, EyeOff } from "lucide-react"
import { Progress } from "@/components/ui/progress"

type RegistrationStep = "email-verification" | "create-account" | "complete-profile"

// Validation types
interface ValidationErrors {
  email?: string
  name?: string
  phoneNumber?: string
  password?: string
  confirmPassword?: string
}

// Loading component to show while suspense is resolving
function LoadingState() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
    </div>
  )
}

// Validation functions
const validateEmail = (email: string): string | undefined => {
  if (!email.trim()) return "Email is required"
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) return "Please enter a valid email address"
  return undefined
}

const validateName = (name: string): string | undefined => {
  if (!name.trim()) return "Business name is required"
  if (name.trim().length < 2) return "Business name must be at least 2 characters"
  if (name.trim().length > 100) return "Business name must be less than 100 characters"
  return undefined
}

const validatePhoneNumber = (phone: string): string | undefined => {
  if (!phone.trim()) return "Phone number is required"
  const phoneRegex = /^[6-9]\d{9}$/
  if (!phoneRegex.test(phone.replace(/\s+/g, ''))) {
    return "Please enter a valid 10-digit Indian mobile number"
  }
  return undefined
}

const validatePassword = (password: string): string | undefined => {
  if (!password) return "Password is required"
  if (password.length < 8) return "Password must be at least 8 characters"
  if (!/(?=.*[a-z])/.test(password)) return "Password must contain at least one lowercase letter"
  if (!/(?=.*[A-Z])/.test(password)) return "Password must contain at least one uppercase letter"
  if (!/(?=.*\d)/.test(password)) return "Password must contain at least one number"
  if (!/(?=.*[@$!%*?&])/.test(password)) return "Password must contain at least one special character"
  return undefined
}

const validateConfirmPassword = (password: string, confirmPassword: string): string | undefined => {
  if (!confirmPassword) return "Please confirm your password"
  if (password !== confirmPassword) return "Passwords do not match"
  return undefined
}

// Main registration component that uses searchParams
function RegistrationForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [step, setStep] = useState<RegistrationStep>("email-verification")
  const [isLoading, setIsLoading] = useState(false)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const [emailSent, setEmailSent] = useState(false)

  // Email verification step
  const [email, setEmail] = useState("")

  // Create account step
  const [name, setName] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [userData, setUserData] = useState<any>(null)

  // Validation states
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Profile completion progress
  const [profileProgress, setProfileProgress] = useState(0)

  // Real-time validation
  const validateField = (fieldName: string, value: string) => {
    let error: string | undefined = undefined

    switch (fieldName) {
      case 'email':
        error = validateEmail(value)
        break
      case 'name':
        error = validateName(value)
        break
      case 'phoneNumber':
        error = validatePhoneNumber(value)
        break
      case 'password':
        error = validatePassword(value)
        // Also revalidate confirm password if it's been touched
        if (touched.confirmPassword) {
          const confirmError = validateConfirmPassword(value, confirmPassword)
          setErrors(prev => ({ ...prev, confirmPassword: confirmError }))
        }
        break
      case 'confirmPassword':
        error = validateConfirmPassword(password, value)
        break
    }

    setErrors(prev => ({ ...prev, [fieldName]: error }))
    return error === undefined
  }

  const handleFieldChange = (fieldName: string, value: string) => {
    switch (fieldName) {
      case 'email':
        setEmail(value)
        break
      case 'name':
        setName(value)
        break
      case 'phoneNumber':
        // Allow only numbers and limit to 10 digits
        const cleanPhone = value.replace(/\D/g, '').slice(0, 10)
        setPhoneNumber(cleanPhone)
        validateField(fieldName, cleanPhone)
        return
      case 'password':
        setPassword(value)
        break
      case 'confirmPassword':
        setConfirmPassword(value)
        break
    }

    if (touched[fieldName]) {
      validateField(fieldName, value)
    }
  }

  const handleFieldBlur = (fieldName: string, value: string) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }))
    validateField(fieldName, value)
  }

  const isFormValid = () => {
    if (step === "email-verification") {
      return validateEmail(email) === undefined
    }

    if (step === "create-account") {
      return (
        validateName(name) === undefined &&
        validatePhoneNumber(phoneNumber) === undefined &&
        validatePassword(password) === undefined &&
        validateConfirmPassword(password, confirmPassword) === undefined
      )
    }

    return false
  }

  useEffect(() => {
    // Check if user is already logged in
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // Check if user is already registered as a vendor
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/check-exists`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ uid: user.uid }),
          })

          const data = await response.json()

          if (data.exists) {
            // If vendor exists and profile is complete, redirect to dashboard
            if (data.profileComplete >= 90) {
              router.push("/dashboard")
            } else {
              // If vendor exists but profile is incomplete, redirect to profile page
              router.push("/profile")
            }
          } else if (user.emailVerified) {
            // If email is verified but vendor doesn't exist, go to create account step
            setUserData({
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
            })
            setEmail(user.email || "")
            setName(user.displayName || "")
            setStep("create-account")
          }
        } catch (error) {
          console.error("Error checking vendor status:", error)
        }
      } else {
        // Check if this is an email sign-in link
        if (isSignInWithEmailLink(auth, window.location.href)) {
          // Get email from localStorage or from URL params
          const emailFromParams = searchParams.get("email")
          const emailForSignIn = emailFromParams || localStorage.getItem("emailForSignIn") || ""

          if (emailForSignIn) {
            setIsLoading(true)
            try {
              await signInWithEmailLink(auth, emailForSignIn, window.location.href)
              localStorage.removeItem("emailForSignIn")
              setEmail(emailForSignIn)
              setStep("create-account")
            } catch (error) {
              console.error("Error signing in with email link:", error)
              toast.error("Failed to verify email. Please try again.")
            } finally {
              setIsLoading(false)
            }
          } else {
            toast.error("Please provide your email to complete verification.")
          }
        }
      }

      setIsCheckingAuth(false)
    })

    return () => unsubscribe()
  }, [router, searchParams])

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    try {
      const provider = new GoogleAuthProvider()
      const result = await signInWithPopup(auth, provider)
      const user = result.user

      // Check if user is already registered as a vendor
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/check-exists`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ uid: user.uid }),
      })

      const data = await response.json()

      if (data.exists) {
        toast.success("Welcome back!")
        router.push(data.profileComplete >= 90 ? "/dashboard" : "/profile")
      } else {
        setUserData({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
        })
        setEmail(user.email || "")
        setName(user.displayName || "")
        setStep("create-account")
      }
    } catch (error) {
      console.error("Google sign-in error:", error)
      toast.error("Failed to sign in with Google")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendEmailLink = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate email
    const emailError = validateEmail(email)
    if (emailError) {
      setErrors({ email: emailError })
      setTouched({ email: true })
      return
    }

    setIsLoading(true)
    try {
      // Check if email is already registered
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/check-exists`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (data.exists) {
        toast.error("This email is already registered. Please login instead.")
        router.push("/login")
        return
      }

      // Send email verification link
      const actionCodeSettings = {
        url: `${window.location.origin}/register?email=${email}`,
        handleCodeInApp: true,
      }

      await sendSignInLinkToEmail(auth, email, actionCodeSettings)
      localStorage.setItem("emailForSignIn", email)
      setEmailSent(true)
      toast.success("Verification email sent! Check your inbox.")
    } catch (error) {
      console.error("Error sending email link:", error)
      toast.error("Failed to send verification email")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateAccount = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate all fields
    const nameError = validateName(name)
    const phoneError = validatePhoneNumber(phoneNumber)
    const passwordError = validatePassword(password)
    const confirmPasswordError = validateConfirmPassword(password, confirmPassword)

    setErrors({
      name: nameError,
      phoneNumber: phoneError,
      password: passwordError,
      confirmPassword: confirmPasswordError,
    })

    setTouched({
      name: true,
      phoneNumber: true,
      password: true,
      confirmPassword: true,
    })

    if (nameError || phoneError || passwordError || confirmPasswordError) {
      toast.error("Please fix the validation errors")
      return
    }

    setIsLoading(true)
    try {
      // Register vendor in the backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: name.trim(),
          email: userData.email || email,
          mobileNumber: phoneNumber,
          password,
          uid: userData.uid,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Failed to register")
      }

      // Refresh the ID token to include the new vendor claim
      await auth.currentUser?.getIdToken(true)

      toast.success("Registration successful!")
      setTimeout(() => {
        toast.success("Please login to continue")
        router.push("/login")
      }, 1000)
    } catch (error) {
      console.error("Registration error:", error)
      toast.error(error instanceof Error ? error.message : "Registration failed")
    } finally {
      setIsLoading(false)
    }
  }

  if (isCheckingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">Vendor Registration</CardTitle>
          <CardDescription className="text-center">
            {step === "email-verification" && "Verify your email to get started"}
            {step === "create-account" && "Create your vendor account"}
            {step === "complete-profile" && "Complete your profile information"}
          </CardDescription>

          {step !== "email-verification" && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Profile completion</span>
                <span>{profileProgress}%</span>
              </div>
              <Progress value={profileProgress} className="h-2" />
            </div>
          )}
        </CardHeader>

        <CardContent>
          {step === "email-verification" && (
            <>
              {!emailSent ? (
                <form onSubmit={handleSendEmailLink} className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      Email address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => handleFieldChange('email', e.target.value)}
                        onBlur={(e) => handleFieldBlur('email', e.target.value)}
                        className={`pl-10 ${errors.email && touched.email ? 'border-red-500 focus:border-red-500' : ''}`}
                        required
                      />
                      {errors.email && touched.email && (
                        <div className="flex items-center mt-1 text-red-500 text-xs">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {errors.email}
                        </div>
                      )}
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-indigo-600 hover:bg-indigo-700" 
                    disabled={isLoading || !isFormValid()}
                  >
                    {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Mail className="mr-2 h-4 w-4" />}
                    Send Verification Email
                  </Button>

                  <div className="relative my-6">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-white text-gray-500">Or continue with</span>
                    </div>
                  </div>

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={handleGoogleSignIn}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <FcGoogle className="mr-2 h-5 w-5" />
                    )}
                    Sign up with Google
                  </Button>
                </form>
              ) : (
                <div className="text-center py-8 space-y-4">
                  <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                  <h3 className="text-lg font-medium">Verification email sent!</h3>
                  <p className="text-sm text-gray-500">
                    We've sent a verification link to <strong>{email}</strong>. Please check your inbox and click the
                    link to continue.
                  </p>
                  <Button variant="outline" className="mt-4" onClick={() => setEmailSent(false)}>
                    Use a different email
                  </Button>
                </div>
              )}
            </>
          )}

          {step === "create-account" && (
            <form onSubmit={handleCreateAccount} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email address
                </label>
                <Input id="email" type="email" value={email} disabled className="bg-gray-100 dark:bg-gray-800" />
                <p className="text-xs text-gray-500">Email verified successfully</p>
              </div>

              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Business Name
                </label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Your business name"
                  value={name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  onBlur={(e) => handleFieldBlur('name', e.target.value)}
                  className={errors.name && touched.name ? 'border-red-500 focus:border-red-500' : ''}
                  required
                />
                {errors.name && touched.name && (
                  <div className="flex items-center mt-1 text-red-500 text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {errors.name}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="phoneNumber" className="text-sm font-medium">
                  Phone Number
                </label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  placeholder="10-digit mobile number"
                  value={phoneNumber}
                  onChange={(e) => handleFieldChange('phoneNumber', e.target.value)}
                  onBlur={(e) => handleFieldBlur('phoneNumber', e.target.value)}
                  className={errors.phoneNumber && touched.phoneNumber ? 'border-red-500 focus:border-red-500' : ''}
                  maxLength={10}
                  required
                />
                {errors.phoneNumber && touched.phoneNumber && (
                  <div className="flex items-center mt-1 text-red-500 text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {errors.phoneNumber}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Create a password"
                    value={password}
                    onChange={(e) => handleFieldChange('password', e.target.value)}
                    onBlur={(e) => handleFieldBlur('password', e.target.value)}
                    className={`pl-10 pr-10 ${errors.password && touched.password ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.password && touched.password && (
                  <div className="flex items-center mt-1 text-red-500 text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {errors.password}
                  </div>
                )}
                <div className="text-xs text-gray-500">
                  Password must contain: 8+ characters, uppercase, lowercase, number, and special character
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium">
                  Confirm Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={confirmPassword}
                    onChange={(e) => handleFieldChange('confirmPassword', e.target.value)}
                    onBlur={(e) => handleFieldBlur('confirmPassword', e.target.value)}
                    className={`pl-10 pr-10 ${errors.confirmPassword && touched.confirmPassword ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.confirmPassword && touched.confirmPassword && (
                  <div className="flex items-center mt-1 text-red-500 text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {errors.confirmPassword}
                  </div>
                )}
              </div>

              <Button 
                type="submit" 
                className="w-full bg-indigo-600 hover:bg-indigo-700" 
                disabled={isLoading || !isFormValid()}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Account
              </Button>
            </form>
          )}
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center text-gray-500">
            Already have an account?{" "}
            <a href="/login" className="text-indigo-600 hover:text-indigo-500">
              Sign in
            </a>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

// Main component that wraps the registration form with Suspense
export default function Register() {
  return (
    <Suspense fallback={<LoadingState />}>
      <RegistrationForm />
    </Suspense>
  )
}