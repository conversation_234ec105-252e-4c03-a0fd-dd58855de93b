"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Filter, Search, Loader2 } from "lucide-react"
import { format } from "date-fns"
import { auth } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"

interface Transaction {
  id: string
  orderId: string
  orderNumber: string
  customer: string
  items: {
    id: string
    name: string
    image: string
    quantity: number
    price: number
    subtotal: number
  }[]
  amount: number
  vendorShare: number
  date: string
  status: "Completed" | "Pending" | "Failed" | "Refunded"
  paymentMethod?: string
}

export function TransactionHistory() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [dateFilter, setDateFilter] = useState<string>("all")
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 1,
  })
  const { toast } = useToast()

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setIsLoading(true)
        const user = auth.currentUser

        if (!user) {
          throw new Error("User not authenticated")
        }

        const token = await user.getIdToken()

        // Build query parameters
        const queryParams = new URLSearchParams()
        queryParams.append("page", pagination.page.toString())
        queryParams.append("limit", pagination.limit.toString())

        if (statusFilter !== "all") {
          queryParams.append("status", statusFilter)
        }

        if (dateFilter !== "all") {
          const today = new Date()
          const startDate = new Date()

          if (dateFilter === "week") {
            startDate.setDate(today.getDate() - 7)
          } else if (dateFilter === "month") {
            startDate.setMonth(today.getMonth() - 1)
          } else if (dateFilter === "year") {
            startDate.setFullYear(today.getFullYear() - 1)
          }

          queryParams.append("startDate", startDate.toISOString())
          queryParams.append("endDate", today.toISOString())
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/payments?${queryParams.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        )

        if (!response.ok) {
          throw new Error("Failed to fetch transactions")
        }

        const data = await response.json()

        if (data.success) {
          // Transform the data to match our Transaction interface
          const formattedTransactions = data.payments.map((payment: any) => ({
            id: payment.id,
            orderId: payment.orderId,
            orderNumber: payment.orderNumber || "N/A",
            customer: payment.customer || "Unknown",
            items: payment.items || [],
            amount: payment.amount,
            vendorShare: payment.vendorShare,
            date: payment.paymentDate,
            status: payment.status,
          }))

          setTransactions(formattedTransactions)
          setPagination(data.pagination)
        } else {
          throw new Error(data.error || "Failed to fetch transactions")
        }
      } catch (error) {
        console.error("Error fetching transactions:", error)
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to fetch transactions",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchTransactions()
  }, [pagination.page, pagination.limit, statusFilter, dateFilter, toast])

  // Filter transactions based on search term
  const filteredTransactions = transactions.filter((transaction) => {
    // Search filter
    return (
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.items.some((item) => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  })

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination((prev) => ({ ...prev, page: newPage }))
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-900/20">Completed</Badge>
      case "Pending":
        return <Badge className="bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200 hover:bg-purple-100 dark:hover:bg-purple-900/20">Pending</Badge>
      case "Refunded":
        return <Badge className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 hover:bg-red-100 dark:hover:bg-red-900/20">Refunded</Badge>
      default:
        return status
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy")
    } catch (error) {
      return dateString
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Transaction History</h2>
            <p className="text-sm text-muted-foreground">View and manage all your rental transactions</p>
          </div>

          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap gap-2 items-center w-full md:w-auto">
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="week">Last 7 Days</SelectItem>
                  <SelectItem value="month">Last 30 Days</SelectItem>
                  <SelectItem value="year">Last Year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="Failed">Failed</SelectItem>
                  <SelectItem value="Refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                className="flex items-center gap-1"
                onClick={() => {
                  // Reset filters
                  setSearchTerm("")
                  setStatusFilter("all")
                  setDateFilter("all")
                  setPagination((prev) => ({ ...prev, page: 1 }))
                }}
              >
                <Filter className="h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
              <span className="ml-2 text-lg">Loading transactions...</span>
            </div>
          ) : filteredTransactions.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transaction ID</TableHead>
                    <TableHead>Order Number</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Item</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Vendor Share</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.id.substring(0, 8)}</TableCell>
                      <TableCell className="text-blue-600">{transaction.orderNumber}</TableCell>
                      <TableCell>{transaction.customer}</TableCell>
                      <TableCell>
                        {transaction.items && transaction.items.length > 0
                          ? transaction.items[0].name +
                            (transaction.items.length > 1 ? ` +${transaction.items.length - 1} more` : "")
                          : "N/A"}
                      </TableCell>
                      <TableCell>₹{transaction.amount.toLocaleString()}</TableCell>
                      <TableCell>₹{transaction.vendorShare.toLocaleString()}</TableCell>
                      <TableCell>{formatDate(transaction.date)}</TableCell>
                      <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-lg font-medium">No transactions found</p>
              <p className="text-muted-foreground">Try adjusting your filters or search term</p>
            </div>
          )}

          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredTransactions.length} of {pagination.total} transactions
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page <= 1}
                onClick={() => handlePageChange(pagination.page - 1)}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page >= pagination.pages}
                onClick={() => handlePageChange(pagination.page + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
