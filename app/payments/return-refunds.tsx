"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Filter, Download, Search, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { useToast } from "@/hooks/use-toast"

interface ReturnItem {
  id: string
  orderId: string
  customer: string
  item: string
  depositAmount: number
  refundAmount?: number
  rentalEndDate: string
  returnDate?: string
  condition?: string
  status: "Pending" | "Completed"
}

export function ReturnRefunds() {
  const { toast } = useToast()
  const [pendingReturns, setPendingReturns] = useState<ReturnItem[]>([
    {
      id: "RET-001",
      orderId: "ORD-1001",
      customer: "<PERSON>",
      item: "Modern Sofa",
      depositAmount: 5000,
      rentalEndDate: "2023-07-15",
      status: "Pending",
    },
    {
      id: "RET-002",
      orderId: "ORD-1002",
      customer: "<PERSON>",
      item: "Dining Table",
      depositAmount: 3500,
      rentalEndDate: "2023-07-18",
      status: "Pending",
    },
    {
      id: "RET-003",
      orderId: "ORD-1004",
      customer: "David Brown",
      item: "Office Desk",
      depositAmount: 2500,
      rentalEndDate: "2023-07-22",
      status: "Pending",
    },
  ])

  const [completedReturns, setCompletedReturns] = useState<ReturnItem[]>([
    {
      id: "RET-004",
      orderId: "ORD-1003",
      customer: "Sarah Williams",
      item: "Queen Bed",
      depositAmount: 8000,
      refundAmount: 6000,
      rentalEndDate: "2023-07-15",
      returnDate: "2023-07-21",
      condition: "Good",
      status: "Completed",
    },
    {
      id: "RET-005",
      orderId: "ORD-1005",
      customer: "Jennifer Davis",
      item: "Bookshelf",
      depositAmount: 2000,
      refundAmount: 1500,
      rentalEndDate: "2023-07-20",
      returnDate: "2023-07-26",
      condition: "Damaged",
      status: "Completed",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("pending-returns")

  const handleProcessReturn = (returnId: string) => {
    toast({
      title: "Return processed",
      description: `Return ${returnId} has been processed successfully.`,
    })
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy")
    } catch (error) {
      return dateString
    }
  }

  const filteredPendingReturns = pendingReturns.filter(
    (item) =>
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.item.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const filteredCompletedReturns = completedReturns.filter(
    (item) =>
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.item.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold">Return & Refund Management</h2>
              <p className="text-sm text-muted-foreground">Process returns and manage deposit refunds</p>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search returns..."
                  className="pl-10 w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button variant="outline" className="flex items-center gap-1">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" className="flex items-center gap-1">
                <Download className="h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          <Tabs defaultValue="pending-returns" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="pending-returns">Pending Returns</TabsTrigger>
              <TabsTrigger value="completed-returns">Completed Returns</TabsTrigger>
            </TabsList>

            <TabsContent value="pending-returns">
              {filteredPendingReturns.length > 0 ? (
                <>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6 flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-purple-800">Pending Returns</h3>
                      <p className="text-sm text-purple-700">
                        You have {filteredPendingReturns.length} pending returns that need to be processed.
                      </p>
                    </div>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Return ID</TableHead>
                        <TableHead>Order ID</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Item</TableHead>
                        <TableHead>Deposit Amount</TableHead>
                        <TableHead>Rental End Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPendingReturns.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">{item.id}</TableCell>
                          <TableCell className="text-blue-600">{item.orderId}</TableCell>
                          <TableCell>{item.customer}</TableCell>
                          <TableCell>{item.item}</TableCell>
                          <TableCell>₹{item.depositAmount.toLocaleString()}</TableCell>
                          <TableCell>{formatDate(item.rentalEndDate)}</TableCell>
                          <TableCell>
                            <Badge className="bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200 hover:bg-purple-100 dark:hover:bg-purple-900/20">Pending</Badge>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              className="bg-purple-600 hover:bg-purple-700"
                              onClick={() => handleProcessReturn(item.id)}
                            >
                              Process Return
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </>
              ) : (
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium mb-2">No pending returns</h3>
                  <p className="text-muted-foreground">All returns have been processed.</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="completed-returns">
              {filteredCompletedReturns.length > 0 ? (
                <>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-green-800">Completed Returns</h3>
                      <p className="text-sm text-green-700">
                        You have processed {filteredCompletedReturns.length} returns successfully.
                      </p>
                    </div>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Return ID</TableHead>
                        <TableHead>Order ID</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Item</TableHead>
                        <TableHead>Deposit Amount</TableHead>
                        <TableHead>Refund Amount</TableHead>
                        <TableHead>Return Date</TableHead>
                        <TableHead>Condition</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCompletedReturns.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">{item.id}</TableCell>
                          <TableCell className="text-blue-600">{item.orderId}</TableCell>
                          <TableCell>{item.customer}</TableCell>
                          <TableCell>{item.item}</TableCell>
                          <TableCell>₹{item.depositAmount.toLocaleString()}</TableCell>
                          <TableCell>₹{item.refundAmount?.toLocaleString()}</TableCell>
                          <TableCell>{formatDate(item.returnDate || "")}</TableCell>
                          <TableCell>
                            <Badge
                              className={
                                item.condition === "Good" 
                                  ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200" 
                                  : "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200"
                              }
                            >
                              {item.condition}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-900/20">Completed</Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </>
              ) : (
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium mb-2">No completed returns</h3>
                  <p className="text-muted-foreground">You haven't processed any returns yet.</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-2">Deposit Refund Guidelines</h2>
          <p className="text-sm text-muted-foreground mb-6">Standard procedures for processing deposit refunds</p>

          <div className="space-y-6">
            <div>
              <h3 className="font-medium mb-2">Condition Assessment</h3>
              <ul className="space-y-2 list-disc pl-5">
                <li>
                  <span className="font-medium">Excellent:</span> No visible wear and tear, like new condition
                </li>
                <li>
                  <span className="font-medium">Good:</span> Minor wear from normal use, no significant damage
                </li>
                <li>
                  <span className="font-medium">Fair:</span> Noticeable wear, minor repairs may be needed
                </li>
                <li>
                  <span className="font-medium">Damaged:</span> Significant damage affecting functionality or appearance
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium mb-2">Deduction Guidelines</h3>
              <ul className="space-y-2 list-disc pl-5">
                <li>
                  <span className="font-medium">Minor scratches:</span> 5-10% of deposit
                </li>
                <li>
                  <span className="font-medium">Stains or discoloration:</span> 10-20% of deposit
                </li>
                <li>
                  <span className="font-medium">Structural damage:</span> 30-50% of deposit
                </li>
                <li>
                  <span className="font-medium">Missing components:</span> Cost of replacement + 10% service fee
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium mb-2">Refund Processing</h3>
              <ul className="space-y-2 list-disc pl-5">
                <li>Refunds are processed within 3-5 business days</li>
                <li>Customers will receive refunds through their original payment method</li>
                <li>All deductions must be documented with photos and detailed descriptions</li>
                <li>Disputes must be escalated to the customer service team</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
