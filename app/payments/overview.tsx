"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Bar<PERSON>hart } from "@/components/ui/charts"
import { auth } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface RentalItem {
  id: string
  name: string
  orderId: string
  amount: number
  status: "Paid" | "Pending"
}

interface OverviewData {
  totalRentReceived: number
  totalRentGrowth: number
  pendingPayments: number
  pendingPaymentsCount: number
  profitPercentage: number
  totalSellingPrice: number
  monthlyRentData: { month: string; rentalAmount: number }[]
  recentRentals: RentalItem[]
}

export function PaymentsOverview() {
  const [overviewData, setOverviewData] = useState<OverviewData>({
    totalRentReceived: 0,
    totalRentGrowth: 0,
    pendingPayments: 0,
    pendingPaymentsCount: 0,
    profitPercentage: 0,
    totalSellingPrice: 0,
    monthlyRentData: [],
    recentRentals: [],
  })
  const [isLoading, setIsLoading] = useState(true)
  const [period, setPeriod] = useState("all")
  const { toast } = useToast()

  useEffect(() => {
    const fetchPaymentSummary = async () => {
      try {
        setIsLoading(true)
        const user = auth.currentUser

        if (!user) {
          throw new Error("User not authenticated")
        }

        const token = await user.getIdToken()

        // Build query parameters
        const queryParams = new URLSearchParams()
        if (period !== "all") {
          queryParams.append("period", period)
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/payments/summary?${queryParams.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        )

        if (!response.ok) {
          throw new Error("Failed to fetch payment summary")
        }

        const data = await response.json()

        if (data.success) {
          setOverviewData(data.summary)
        } else {
          throw new Error(data.error || "Failed to fetch payment summary")
        }
      } catch (error) {
        console.error("Error fetching payment summary:", error)
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to fetch payment summary",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchPaymentSummary()
  }, [period, toast])

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Payment Overview</h3>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Time</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
          <span className="ml-2 text-lg">Loading payment data...</span>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col">
                  <span className="text-sm text-muted-foreground">Total Rent Received</span>
                  <span className="text-3xl font-bold">₹{overviewData.totalRentReceived.toLocaleString()}</span>
                  <span
                                    className={`text-xs mt-1 ${overviewData.totalRentGrowth >= 0 ? "text-green-500 dark:text-green-400" : "text-red-500 dark:text-red-400"}`}
                  >
                    {overviewData.totalRentGrowth >= 0 ? "+" : ""}
                    {overviewData.totalRentGrowth}% from last month
                  </span>
                  <Progress value={100} className="h-2 mt-4" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col">
                  <span className="text-sm text-muted-foreground">Pending Payments</span>
                  <span className="text-3xl font-bold">₹{overviewData.pendingPayments.toLocaleString()}</span>
                  <span className="text-xs text-muted-foreground mt-1">
                    {overviewData.pendingPaymentsCount} payments pending
                  </span>
                  <Progress
                    value={70}
                    className="h-2 mt-4"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Profit Percentage</span>
                <span className="text-3xl font-bold">{overviewData.profitPercentage}%</span>
                <span className="text-xs text-muted-foreground mt-1">
                  Of total selling price (₹{overviewData.totalSellingPrice.toLocaleString()})
                </span>
                <Progress
                  value={overviewData.profitPercentage}
                  className="h-2 mt-4"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold">Monthly Rent Overview</h3>
                <p className="text-sm text-muted-foreground">Monthly rental income for the past 6 months</p>
              </div>
              {overviewData.monthlyRentData.length > 0 ? (
                <div className="h-[300px]">
                  <BarChart
                    data={overviewData.monthlyRentData}
                    categories={["rentalAmount"]}
                    index="month"
                    colors={["#8b5cf6"]}
                    valueFormatter={(value) => `₹${value.toLocaleString()}`}
                    showLegend={false}
                    showYAxis={true}
                    showXAxis={true}
                    height={300}
                  />
                </div>
              ) : (
                <div className="flex justify-center items-center h-[300px]">
                  <p className="text-muted-foreground">No rental data available</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h3 className="text-lg font-semibold">Recent Furniture Rentals</h3>
                  <p className="text-sm text-muted-foreground">Your most recent rental transactions</p>
                </div>
              </div>

              {overviewData.recentRentals.length > 0 ? (
                <div className="space-y-4">
                  {overviewData.recentRentals.map((rental) => (
                    <div key={rental.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                        <div>
                          <h4 className="font-medium">{rental.name}</h4>
                          <p className="text-sm text-muted-foreground">Order {rental.orderId}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <span className="font-semibold">₹{rental.amount.toLocaleString()}</span>
                        <span
                          className={`text-xs px-2 py-1 rounded-full ${
                            rental.status === "Paid" 
                              ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200" 
                              : "bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200"
                          }`}
                        >
                          {rental.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">No recent rental transactions</p>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
